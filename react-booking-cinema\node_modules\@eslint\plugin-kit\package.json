{"name": "@eslint/plugin-kit", "version": "0.3.4", "description": "Utilities for building ESLint plugins.", "author": "<PERSON>", "type": "module", "main": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {"require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}}, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/eslint/rewrite.git", "directory": "packages/plugin-kit"}, "bugs": {"url": "https://github.com/eslint/rewrite/issues"}, "homepage": "https://github.com/eslint/rewrite/tree/main/packages/plugin-kit#readme", "scripts": {"build:dedupe-types": "node ../../tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js", "build:cts": "node ../../tools/build-cts.js dist/esm/index.d.ts dist/cjs/index.d.cts", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts", "pretest": "npm run build", "test": "mocha tests/", "test:coverage": "c8 npm test", "test:jsr": "npx jsr@latest publish --dry-run", "test:types": "tsc -p tests/types/tsconfig.json"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "license": "Apache-2.0", "dependencies": {"@eslint/core": "^0.15.1", "levn": "^0.4.1"}, "devDependencies": {"@types/levn": "^0.4.0", "rollup-plugin-copy": "^3.5.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}