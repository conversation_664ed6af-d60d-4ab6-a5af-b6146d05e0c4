// functions
import { createStore, legacy_createStore } from './createStore'
import combineReducers from './combineReducers'
import bindActionCreators from './bindActionCreators'
import applyMiddleware from './applyMiddleware'
import compose from './compose'
import isAction from './utils/isAction'
import isPlainObject from './utils/isPlainObject'
import __DO_NOT_USE__ActionTypes from './utils/actionTypes'

// types
// store
export {
  Dispatch,
  Unsubscribe,
  Observable,
  Observer,
  Store,
  StoreCreator,
  StoreEnhancer,
  StoreEnhancerStoreCreator
} from './types/store'
// reducers
export {
  Reducer,
  ReducersMapObject,
  StateFromReducersMapObject,
  ReducerFromReducersMapObject,
  ActionFromReducer,
  ActionFromReducersMapObject,
  PreloadedStateShapeFromReducersMapObject
} from './types/reducers'
// action creators
export { ActionCreator, ActionCreatorsMapObject } from './types/actions'
// middleware
export { MiddlewareAPI, Middleware } from './types/middleware'
// actions
export { Action, UnknownAction, AnyAction } from './types/actions'

export {
  createStore,
  legacy_createStore,
  combineReducers,
  bindActionCreators,
  applyMiddleware,
  compose,
  isAction,
  isPlainObject,
  __DO_NOT_USE__ActionTypes
}
