import { useSelector, useDispatch } from 'react-redux';
import { cancelBookingFromHistory } from '../store/slices/bookingSlice';

const BookingHistory = () => {
  const dispatch = useDispatch();
  const { bookingHistory } = useSelector(state => state.booking);

  const handleCancelBooking = (bookingId) => {
    if (window.confirm('Bạn có chắc chắn muốn hủy vé này?')) {
      dispatch(cancelBookingFromHistory(bookingId));
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      confirmed: { text: 'Đã xác nhận', className: 'status-confirmed' },
      cancelled: { text: 'Đã hủy', className: 'status-cancelled' }
    };
    
    const config = statusConfig[status] || { text: status, className: '' };
    
    return (
      <span className={`status-badge ${config.className}`}>
        {config.text}
      </span>
    );
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="booking-history">
      <div className="container">
        <h2>Lịch sử đặt vé</h2>
        
        {bookingHistory.length === 0 ? (
          <div className="empty-history">
            <div className="empty-icon">🎬</div>
            <h3>Chưa có lịch sử đặt vé</h3>
            <p>Bạn chưa đặt vé nào. Hãy chọn phim và đặt vé ngay!</p>
            <a href="/" className="btn-primary">Đặt vé ngay</a>
          </div>
        ) : (
          <div className="history-list">
            {bookingHistory.map(booking => (
              <div key={booking.bookingId} className="booking-card">
                <div className="booking-header">
                  <div className="booking-id">
                    <strong>Mã đặt vé: {booking.bookingId}</strong>
                    {getStatusBadge(booking.status)}
                  </div>
                  <div className="booking-date">
                    {formatDate(booking.bookingDate)}
                  </div>
                </div>
                
                <div className="booking-body">
                  <div className="movie-info">
                    <h4>{booking.movie.title}</h4>
                    <p>Suất chiếu: {booking.showtime}</p>
                    <p>Thể loại: {booking.movie.genre}</p>
                    <p>Thời lượng: {booking.movie.duration}</p>
                  </div>
                  
                  <div className="seat-info">
                    <h5>Thông tin ghế:</h5>
                    <div className="seats-list">
                      {booking.seats.map(seat => (
                        <span key={seat.id} className={`seat-tag ${seat.type}`}>
                          {seat.id}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div className="customer-info">
                    <h5>Thông tin khách hàng:</h5>
                    <p><strong>Tên:</strong> {booking.customerInfo.name}</p>
                    <p><strong>Email:</strong> {booking.customerInfo.email}</p>
                    <p><strong>SĐT:</strong> {booking.customerInfo.phone}</p>
                  </div>
                </div>
                
                <div className="booking-footer">
                  <div className="total-price">
                    <strong>Tổng tiền: {booking.totalPrice.toLocaleString('vi-VN')}đ</strong>
                  </div>
                  
                  {booking.status === 'confirmed' && (
                    <div className="booking-actions">
                      <button 
                        className="btn-cancel"
                        onClick={() => handleCancelBooking(booking.bookingId)}
                      >
                        Hủy vé
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BookingHistory;
