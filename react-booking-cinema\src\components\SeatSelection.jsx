import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toggleSeat, calculateTotalPrice } from "../store/slices/seatsSlice";
import { startBooking } from "../store/slices/bookingSlice";
import { useEffect } from "react";

const SeatSelection = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { selectedMovie, selectedShowtime } = useSelector(
    (state) => state.movies
  );
  const { seats, selectedSeats, totalPrice } = useSelector(
    (state) => state.seats
  );

  useEffect(() => {
    if (!selectedMovie || !selectedShowtime) {
      navigate("/");
      return;
    }

    // Calculate total price when selected seats change
    if (selectedSeats.length > 0) {
      dispatch(calculateTotalPrice(selectedMovie.price));
    }
  }, [selectedSeats, selectedMovie, selectedShowtime, dispatch, navigate]);

  const handleSeatClick = (seatId) => {
    dispatch(toggleSeat(seatId));
  };

  const handleContinueToBooking = () => {
    if (selectedSeats.length > 0) {
      dispatch(
        startBooking({
          movie: selectedMovie,
          showtime: selectedShowtime,
          seats: selectedSeats,
          totalPrice: totalPrice,
        })
      );
      navigate("/booking");
    }
  };

  const getSeatClassName = (seat) => {
    let className = `seat ${seat.type}`;
    if (seat.isOccupied) className += " occupied";
    if (seat.isSelected) className += " selected";
    return className;
  };

  // Group seats by row
  const seatsByRow = seats.reduce((acc, seat) => {
    if (!acc[seat.row]) acc[seat.row] = [];
    acc[seat.row].push(seat);
    return acc;
  }, {});

  if (!selectedMovie || !selectedShowtime) {
    return <div>Vui lòng chọn phim và suất chiếu trước</div>;
  }

  return (
    <div className="seat-selection">
      <div className="seat-selection-container">
        {/* Left side - Cinema layout */}
        <div className="cinema-section">
          <div className="cinema-header">
            <h2>ĐẶT VÉ XEM PHIM CYBERLEARN.VN</h2>
          </div>

          <div className="screen-section">
            <div className="screen">MÀN HÌNH</div>
          </div>

          <div className="seat-map">
            {Object.entries(seatsByRow).map(([row, rowSeats]) => (
              <div key={row} className="seat-row">
                <div className="row-label">{row}</div>
                <div className="seats">
                  {rowSeats.map((seat) => (
                    <button
                      key={seat.id}
                      className={getSeatClassName(seat)}
                      onClick={() => handleSeatClick(seat.id)}
                      disabled={seat.isOccupied}
                      title={`Ghế ${seat.id} - ${
                        seat.type === "standard"
                          ? "Thường"
                          : seat.type === "vip"
                          ? "VIP"
                          : "Đôi"
                      }`}
                    >
                      {seat.number}
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Right side - Booking info */}
        <div className="booking-sidebar">
          <div className="booking-info-card">
            <h3>DANH SÁCH GHẾ BẠN CHỌN</h3>

            <div className="legend">
              <div className="legend-item">
                <div className="seat-demo standard"></div>
                <span>Ghế đã đặt</span>
              </div>
              <div className="legend-item">
                <div className="seat-demo vip"></div>
                <span>Ghế thường</span>
              </div>
              <div className="legend-item">
                <div className="seat-demo couple"></div>
                <span>Ghế VIP</span>
              </div>
              <div className="legend-item">
                <div className="seat-demo selected"></div>
                <span>Ghế đang chọn</span>
              </div>
            </div>

            <div className="movie-info-sidebar">
              <h4>{selectedMovie.title}</h4>
              <p>Suất: {selectedShowtime}</p>
            </div>

            {selectedSeats.length > 0 ? (
              <div className="selected-seats-info">
                <h4>Ghế</h4>
                <div className="selected-seats-list">
                  {selectedSeats.map((seat) => (
                    <div key={seat.id} className="seat-item">
                      <span className="seat-name">{seat.id}</span>
                      <span className="seat-price">
                        {selectedMovie.price[seat.type].toLocaleString("vi-VN")}
                      </span>
                    </div>
                  ))}
                </div>

                <div className="total-section">
                  <div className="total-price">
                    <span>Tổng Tiền</span>
                    <span className="price">
                      {totalPrice.toLocaleString("vi-VN")}
                    </span>
                  </div>
                </div>

                <button
                  className="continue-btn"
                  onClick={handleContinueToBooking}
                >
                  ĐẶT VÉ
                </button>
              </div>
            ) : (
              <div className="no-seats-selected">
                <p>Hãy chọn ghế</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SeatSelection;
