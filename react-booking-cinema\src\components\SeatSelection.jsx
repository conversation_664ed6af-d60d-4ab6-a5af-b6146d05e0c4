import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toggleSeat, calculateTotalPrice } from '../store/slices/seatsSlice';
import { startBooking } from '../store/slices/bookingSlice';
import { useEffect } from 'react';

const SeatSelection = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { selectedMovie, selectedShowtime } = useSelector(state => state.movies);
  const { seats, selectedSeats, totalPrice } = useSelector(state => state.seats);

  useEffect(() => {
    if (!selectedMovie || !selectedShowtime) {
      navigate('/');
      return;
    }
    
    // Calculate total price when selected seats change
    if (selectedSeats.length > 0) {
      dispatch(calculateTotalPrice(selectedMovie.price));
    }
  }, [selectedSeats, selectedMovie, selectedShowtime, dispatch, navigate]);

  const handleSeatClick = (seatId) => {
    dispatch(toggleSeat(seatId));
  };

  const handleContinueToBooking = () => {
    if (selectedSeats.length > 0) {
      dispatch(startBooking({
        movie: selectedMovie,
        showtime: selectedShowtime,
        seats: selectedSeats,
        totalPrice: totalPrice
      }));
      navigate('/booking');
    }
  };

  const getSeatClassName = (seat) => {
    let className = `seat ${seat.type}`;
    if (seat.isOccupied) className += ' occupied';
    if (seat.isSelected) className += ' selected';
    return className;
  };

  // Group seats by row
  const seatsByRow = seats.reduce((acc, seat) => {
    if (!acc[seat.row]) acc[seat.row] = [];
    acc[seat.row].push(seat);
    return acc;
  }, {});

  if (!selectedMovie || !selectedShowtime) {
    return <div>Vui lòng chọn phim và suất chiếu trước</div>;
  }

  return (
    <div className="seat-selection">
      <div className="container">
        <div className="cinema-layout">
          <div className="screen-section">
            <div className="screen">MÀN HÌNH</div>
          </div>

          <div className="seats-section">
            <div className="seat-map">
              {Object.entries(seatsByRow).map(([row, rowSeats]) => (
                <div key={row} className="seat-row">
                  <div className="row-label">{row}</div>
                  <div className="seats">
                    {rowSeats.map(seat => (
                      <button
                        key={seat.id}
                        className={getSeatClassName(seat)}
                        onClick={() => handleSeatClick(seat.id)}
                        disabled={seat.isOccupied}
                        title={`Ghế ${seat.id} - ${seat.type === 'standard' ? 'Thường' : seat.type === 'vip' ? 'VIP' : 'Đôi'}`}
                      >
                        {seat.number}
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="legend">
            <div className="legend-item">
              <div className="seat standard"></div>
              <span>Ghế thường ({selectedMovie.price.standard.toLocaleString('vi-VN')}đ)</span>
            </div>
            <div className="legend-item">
              <div className="seat vip"></div>
              <span>Ghế VIP ({selectedMovie.price.vip.toLocaleString('vi-VN')}đ)</span>
            </div>
            <div className="legend-item">
              <div className="seat couple"></div>
              <span>Ghế đôi ({selectedMovie.price.couple.toLocaleString('vi-VN')}đ)</span>
            </div>
            <div className="legend-item">
              <div className="seat occupied"></div>
              <span>Đã đặt</span>
            </div>
            <div className="legend-item">
              <div className="seat selected"></div>
              <span>Đang chọn</span>
            </div>
          </div>
        </div>

        <div className="booking-summary">
          <div className="movie-details">
            <h3>{selectedMovie.title}</h3>
            <p>Suất chiếu: {selectedShowtime}</p>
          </div>

          {selectedSeats.length > 0 && (
            <div className="selected-seats-info">
              <h4>Ghế đã chọn:</h4>
              <div className="selected-seats-list">
                {selectedSeats.map(seat => (
                  <span key={seat.id} className="selected-seat-tag">
                    {seat.id}
                  </span>
                ))}
              </div>
              <div className="total-price">
                <strong>Tổng tiền: {totalPrice.toLocaleString('vi-VN')}đ</strong>
              </div>
              <button 
                className="continue-btn"
                onClick={handleContinueToBooking}
              >
                Tiếp tục đặt vé
              </button>
            </div>
          )}

          {selectedSeats.length === 0 && (
            <div className="no-seats-selected">
              <p>Vui lòng chọn ghế để tiếp tục</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SeatSelection;
