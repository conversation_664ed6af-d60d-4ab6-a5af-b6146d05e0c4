import React, { useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form, Row, Col, Spinner } from "react-bootstrap";
import ErrorModal from "./ErrorModal";

const PaymentModal = ({ show, onHide, bookingData, onPaymentSuccess }) => {
  const [selectedMethod, setSelectedMethod] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [formData, setFormData] = useState({
    cardNumber: "",
    expiryDate: "",
    cvv: "",
    cardName: "",
    phoneNumber: "",
    email: "",
  });

  const paymentMethods = [
    { id: "visa", name: "Visa", icon: "💳" },
    { id: "mastercard", name: "Master<PERSON><PERSON>", icon: "💳" },
    { id: "momo", name: "<PERSON><PERSON><PERSON>", icon: "📱" },
    { id: "zalopay", name: "Zalo<PERSON><PERSON>", icon: "💰" },
    { id: "banking", name: "Internet Banking", icon: "🏦" },
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePayment = async () => {
    if (!selectedMethod) {
      setErrorMessage("Vui lòng chọn phương thức thanh toán");
      setShowErrorModal(true);
      return;
    }

    // Validate form for card payments
    if (["visa", "mastercard"].includes(selectedMethod)) {
      if (
        !formData.cardNumber ||
        !formData.expiryDate ||
        !formData.cvv ||
        !formData.cardName
      ) {
        setErrorMessage("Vui lòng điền đầy đủ thông tin thẻ");
        setShowErrorModal(true);
        return;
      }
    }

    if (!formData.phoneNumber || !formData.email) {
      setErrorMessage("Vui lòng điền đầy đủ thông tin liên hệ");
      setShowErrorModal(true);
      return;
    }

    setIsProcessing(true);

    // Simulate payment processing with random success/failure
    setTimeout(() => {
      setIsProcessing(false);

      // 90% success rate
      const isSuccess = Math.random() > 0.1;

      if (isSuccess) {
        onHide();
        onPaymentSuccess({
          ...bookingData,
          paymentMethod: selectedMethod,
          paymentTime: new Date().toISOString(),
          transactionId: "TXN" + Date.now(),
        });
      } else {
        setErrorMessage(
          "Thanh toán thất bại. Vui lòng kiểm tra lại thông tin và thử lại."
        );
        setShowErrorModal(true);
      }
    }, 3000);
  };

  const resetForm = () => {
    setSelectedMethod("");
    setShowErrorModal(false);
    setErrorMessage("");
    setFormData({
      cardNumber: "",
      expiryDate: "",
      cvv: "",
      cardName: "",
      phoneNumber: "",
      email: "",
    });
  };

  const handleClose = () => {
    resetForm();
    onHide();
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      size="lg"
      centered
      backdrop="static"
      className="payment-modal"
    >
      <Modal.Header closeButton>
        <Modal.Title>
          <i className="fas fa-credit-card me-2"></i>
          Thanh toán đặt vé
        </Modal.Title>
      </Modal.Header>

      <Modal.Body>
        {/* Booking Summary */}
        <div className="booking-summary mb-4">
          <h5 className="text-primary mb-3">Thông tin đặt vé</h5>
          <div className="card bg-light">
            <div className="card-body">
              <Row>
                <Col md={6}>
                  <strong>Phim:</strong> {bookingData?.movieTitle}
                </Col>
                <Col md={6}>
                  <strong>Suất chiếu:</strong> {bookingData?.showtime}
                </Col>
                <Col md={6}>
                  <strong>Ghế:</strong> {bookingData?.seats?.join(", ")}
                </Col>
                <Col md={6}>
                  <strong className="text-success">
                    Tổng tiền:{" "}
                    {bookingData?.totalPrice?.toLocaleString("vi-VN")}đ
                  </strong>
                </Col>
              </Row>
            </div>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="payment-section mb-4">
          <h5 className="mb-3">Chọn phương thức thanh toán</h5>
          <div className="payment-methods">
            {paymentMethods.map((method) => (
              <div
                key={method.id}
                className={`payment-method ${
                  selectedMethod === method.id ? "selected" : ""
                }`}
                onClick={() => setSelectedMethod(method.id)}
              >
                <div style={{ fontSize: "2rem" }}>{method.icon}</div>
                <div className="fw-bold">{method.name}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Payment Form */}
        {selectedMethod && ["visa", "mastercard"].includes(selectedMethod) && (
          <div className="payment-form">
            <h5 className="mb-3">Thông tin thẻ</h5>
            <Form>
              <Row>
                <Col md={12}>
                  <Form.Group className="mb-3">
                    <Form.Label>Số thẻ</Form.Label>
                    <Form.Control
                      type="text"
                      name="cardNumber"
                      value={formData.cardNumber}
                      onChange={handleInputChange}
                      placeholder="1234 5678 9012 3456"
                      maxLength="19"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Ngày hết hạn</Form.Label>
                    <Form.Control
                      type="text"
                      name="expiryDate"
                      value={formData.expiryDate}
                      onChange={handleInputChange}
                      placeholder="MM/YY"
                      maxLength="5"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>CVV</Form.Label>
                    <Form.Control
                      type="text"
                      name="cvv"
                      value={formData.cvv}
                      onChange={handleInputChange}
                      placeholder="123"
                      maxLength="3"
                    />
                  </Form.Group>
                </Col>
                <Col md={12}>
                  <Form.Group className="mb-3">
                    <Form.Label>Tên chủ thẻ</Form.Label>
                    <Form.Control
                      type="text"
                      name="cardName"
                      value={formData.cardName}
                      onChange={handleInputChange}
                      placeholder="NGUYEN VAN A"
                    />
                  </Form.Group>
                </Col>
              </Row>
            </Form>
          </div>
        )}

        {/* Contact Info */}
        <div className="contact-info">
          <h5 className="mb-3">Thông tin liên hệ</h5>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Số điện thoại</Form.Label>
                <Form.Control
                  type="tel"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleInputChange}
                  placeholder="0123456789"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Email</Form.Label>
                <Form.Control
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                />
              </Form.Group>
            </Col>
          </Row>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <Button variant="outline-secondary" onClick={handleClose}>
          Hủy
        </Button>
        <Button
          variant="success"
          onClick={handlePayment}
          disabled={!selectedMethod || isProcessing}
          className="px-4"
        >
          {isProcessing ? (
            <>
              <Spinner
                as="span"
                animation="border"
                size="sm"
                role="status"
                aria-hidden="true"
                className="me-2"
              />
              Đang xử lý...
            </>
          ) : (
            `Thanh toán ${bookingData?.totalPrice?.toLocaleString("vi-VN")}đ`
          )}
        </Button>
      </Modal.Footer>

      {/* Error Modal */}
      <ErrorModal
        show={showErrorModal}
        onHide={() => setShowErrorModal(false)}
        title="Lỗi thanh toán"
        message={errorMessage}
        type="error"
      />
    </Modal>
  );
};

export default PaymentModal;
