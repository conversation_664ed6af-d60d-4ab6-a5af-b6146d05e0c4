import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  bookingHistory: [],
  currentBooking: {
    movie: null,
    showtime: null,
    seats: [],
    customerInfo: {
      name: '',
      email: '',
      phone: ''
    },
    totalPrice: 0,
    bookingDate: null,
    bookingId: null
  },
  isBookingInProgress: false
};

const bookingSlice = createSlice({
  name: 'booking',
  initialState,
  reducers: {
    startBooking: (state, action) => {
      const { movie, showtime, seats, totalPrice } = action.payload;
      state.currentBooking = {
        movie,
        showtime,
        seats,
        customerInfo: {
          name: '',
          email: '',
          phone: ''
        },
        totalPrice,
        bookingDate: new Date().toISOString(),
        bookingId: `BK${Date.now()}`
      };
      state.isBookingInProgress = true;
    },
    updateCustomerInfo: (state, action) => {
      state.currentBooking.customerInfo = {
        ...state.currentBooking.customerInfo,
        ...action.payload
      };
    },
    confirmBooking: (state) => {
      if (state.isBookingInProgress) {
        state.bookingHistory.push({
          ...state.currentBooking,
          status: 'confirmed'
        });
        
        // Reset current booking
        state.currentBooking = {
          movie: null,
          showtime: null,
          seats: [],
          customerInfo: {
            name: '',
            email: '',
            phone: ''
          },
          totalPrice: 0,
          bookingDate: null,
          bookingId: null
        };
        state.isBookingInProgress = false;
      }
    },
    cancelBooking: (state) => {
      state.currentBooking = {
        movie: null,
        showtime: null,
        seats: [],
        customerInfo: {
          name: '',
          email: '',
          phone: ''
        },
        totalPrice: 0,
        bookingDate: null,
        bookingId: null
      };
      state.isBookingInProgress = false;
    },
    cancelBookingFromHistory: (state, action) => {
      const bookingId = action.payload;
      const booking = state.bookingHistory.find(b => b.bookingId === bookingId);
      if (booking) {
        booking.status = 'cancelled';
      }
    }
  }
});

export const {
  startBooking,
  updateCustomerInfo,
  confirmBooking,
  cancelBooking,
  cancelBookingFromHistory
} = bookingSlice.actions;

export default bookingSlice.reducer;
