import { useSelector, useDispatch } from 'react-redux';
import { selectMovie, selectShowtime } from '../store/slices/moviesSlice';
import { clearSelectedSeats } from '../store/slices/seatsSlice';
import { useNavigate } from 'react-router-dom';

const MovieList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { movies, selectedMovie, selectedShowtime } = useSelector(state => state.movies);

  const handleMovieSelect = (movie) => {
    dispatch(selectMovie(movie));
    dispatch(clearSelectedSeats());
  };

  const handleShowtimeSelect = (showtime) => {
    dispatch(selectShowtime(showtime));
  };

  const handleContinueToSeatSelection = () => {
    if (selectedMovie && selectedShowtime) {
      navigate('/seat-selection');
    }
  };

  return (
    <div className="movie-list">
      <div className="container">
        <h2><PERSON>h sách phim</h2>
        
        <div className="movies-grid">
          {movies.map(movie => (
            <div 
              key={movie.id} 
              className={`movie-card ${selectedMovie?.id === movie.id ? 'selected' : ''}`}
              onClick={() => handleMovieSelect(movie)}
            >
              <div className="movie-poster">
                <img src={movie.poster} alt={movie.title} />
                <div className="movie-overlay">
                  <div className="movie-rating">⭐ {movie.rating}</div>
                </div>
              </div>
              
              <div className="movie-info">
                <h3 className="movie-title">{movie.title}</h3>
                <p className="movie-duration">{movie.duration}</p>
                <p className="movie-genre">{movie.genre}</p>
                
                <div className="movie-prices">
                  <div className="price-item">
                    <span className="seat-type standard">Thường</span>
                    <span className="price">{movie.price.standard.toLocaleString('vi-VN')}đ</span>
                  </div>
                  <div className="price-item">
                    <span className="seat-type vip">VIP</span>
                    <span className="price">{movie.price.vip.toLocaleString('vi-VN')}đ</span>
                  </div>
                  <div className="price-item">
                    <span className="seat-type couple">Đôi</span>
                    <span className="price">{movie.price.couple.toLocaleString('vi-VN')}đ</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {selectedMovie && (
          <div className="showtime-selection">
            <h3>Chọn suất chiếu cho "{selectedMovie.title}"</h3>
            <div className="showtimes">
              {selectedMovie.showtimes.map(time => (
                <button
                  key={time}
                  className={`showtime-btn ${selectedShowtime === time ? 'selected' : ''}`}
                  onClick={() => handleShowtimeSelect(time)}
                >
                  {time}
                </button>
              ))}
            </div>
            
            {selectedShowtime && (
              <div className="continue-section">
                <button 
                  className="continue-btn"
                  onClick={handleContinueToSeatSelection}
                >
                  Tiếp tục chọn ghế
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MovieList;
