import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col } from 'react-bootstrap';

const ConfirmBookingModal = ({ show, onHide, bookingData, onConfirm }) => {
  if (!bookingData) return null;

  return (
    <Modal 
      show={show} 
      onHide={onHide} 
      centered 
      size="lg"
      className="confirm-booking-modal"
    >
      <Modal.Header closeButton className="bg-primary text-white">
        <Modal.Title>
          <i className="fas fa-check-circle me-2"></i>
          Xác nhận thông tin đặt vé
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body className="p-4">
        <div className="booking-confirmation">
          <div className="text-center mb-4">
            <h4 className="text-primary">Vui lòng kiểm tra thông tin đặt vé</h4>
            <p className="text-muted">Đ<PERSON><PERSON> b<PERSON>o tất cả thông tin đều ch<PERSON>h xác tr<PERSON>ớ<PERSON> khi tiếp tục</p>
          </div>

          <div className="booking-details-card">
            <div className="card border-0 shadow-sm">
              <div className="card-header bg-light">
                <h5 className="mb-0 text-primary">
                  <i className="fas fa-film me-2"></i>
                  Thông tin phim
                </h5>
              </div>
              <div className="card-body">
                <Row>
                  <Col md={6}>
                    <div className="info-item mb-3">
                      <label className="text-muted small">Tên phim:</label>
                      <div className="fw-bold">{bookingData.movieTitle}</div>
                    </div>
                  </Col>
                  <Col md={6}>
                    <div className="info-item mb-3">
                      <label className="text-muted small">Suất chiếu:</label>
                      <div className="fw-bold">{bookingData.showtime}</div>
                    </div>
                  </Col>
                </Row>
              </div>
            </div>

            <div className="card border-0 shadow-sm mt-3">
              <div className="card-header bg-light">
                <h5 className="mb-0 text-success">
                  <i className="fas fa-couch me-2"></i>
                  Thông tin ghế
                </h5>
              </div>
              <div className="card-body">
                <Row>
                  <Col md={6}>
                    <div className="info-item mb-3">
                      <label className="text-muted small">Ghế đã chọn:</label>
                      <div className="fw-bold">
                        {bookingData.seats?.join(', ')}
                      </div>
                    </div>
                  </Col>
                  <Col md={6}>
                    <div className="info-item mb-3">
                      <label className="text-muted small">Số lượng ghế:</label>
                      <div className="fw-bold">{bookingData.seats?.length} ghế</div>
                    </div>
                  </Col>
                </Row>
              </div>
            </div>

            <div className="card border-0 shadow-sm mt-3">
              <div className="card-header bg-light">
                <h5 className="mb-0 text-warning">
                  <i className="fas fa-money-bill-wave me-2"></i>
                  Thông tin thanh toán
                </h5>
              </div>
              <div className="card-body">
                <Row>
                  <Col md={6}>
                    <div className="info-item mb-3">
                      <label className="text-muted small">Mã đặt vé:</label>
                      <div className="fw-bold text-info">{bookingData.bookingId}</div>
                    </div>
                  </Col>
                  <Col md={6}>
                    <div className="info-item mb-3">
                      <label className="text-muted small">Tổng tiền:</label>
                      <div className="fw-bold text-success fs-5">
                        {bookingData.totalPrice?.toLocaleString('vi-VN')}đ
                      </div>
                    </div>
                  </Col>
                </Row>
              </div>
            </div>
          </div>

          <div className="alert alert-info mt-4" role="alert">
            <i className="fas fa-info-circle me-2"></i>
            <strong>Lưu ý:</strong> Sau khi xác nhận, bạn sẽ được chuyển đến trang thanh toán. 
            Vé sẽ được giữ trong 15 phút để bạn hoàn tất thanh toán.
          </div>
        </div>
      </Modal.Body>
      
      <Modal.Footer className="border-0 pt-0">
        <div className="d-flex justify-content-between w-100">
          <Button 
            variant="outline-secondary" 
            onClick={onHide}
            size="lg"
          >
            <i className="fas fa-arrow-left me-2"></i>
            Quay lại chỉnh sửa
          </Button>
          <Button 
            variant="success" 
            onClick={onConfirm}
            size="lg"
            className="px-4"
          >
            <i className="fas fa-credit-card me-2"></i>
            Xác nhận & Thanh toán
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default ConfirmBookingModal;
