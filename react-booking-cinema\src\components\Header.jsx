import { Link, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";

const Header = () => {
  const location = useLocation();
  const { selectedMovie, selectedShowtime } = useSelector(
    (state) => state.movies
  );
  const { selectedSeats, totalPrice } = useSelector((state) => state.seats);
  const { isBookingInProgress } = useSelector((state) => state.booking);

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          <Link to="/" className="logo">
            <h1>🎬 LONG ANH CINEMA</h1>
          </Link>

          <nav className="nav">
            <Link
              to="/"
              className={
                location.pathname === "/" ? "nav-link active" : "nav-link"
              }
            >
              Trang chủ
            </Link>
            <Link
              to="/booking-history"
              className={
                location.pathname === "/booking-history"
                  ? "nav-link active"
                  : "nav-link"
              }
            >
              <PERSON><PERSON><PERSON> sử đặt vé
            </Link>
          </nav>
        </div>

        {/* Booking Progress Bar - Hide on seat selection page */}
        {(selectedMovie || isBookingInProgress) &&
          location.pathname !== "/seat-selection" && (
            <div className="booking-progress">
              <div className="progress-steps">
                <div className={`step ${selectedMovie ? "completed" : ""}`}>
                  <span className="step-number">1</span>
                  <span className="step-label">Chọn phim</span>
                </div>
                <div
                  className={`step ${
                    selectedSeats.length > 0
                      ? "completed"
                      : selectedMovie
                      ? "active"
                      : ""
                  }`}
                >
                  <span className="step-number">2</span>
                  <span className="step-label">Chọn ghế</span>
                </div>
                <div className={`step ${isBookingInProgress ? "active" : ""}`}>
                  <span className="step-number">3</span>
                  <span className="step-label">Thanh toán</span>
                </div>
              </div>

              {selectedMovie && (
                <div className="booking-info">
                  <div className="movie-info">
                    <strong>{selectedMovie.title}</strong>
                    {selectedShowtime && <span> - {selectedShowtime}</span>}
                  </div>
                  {selectedSeats.length > 0 && (
                    <div className="seat-info">
                      <span>
                        Ghế: {selectedSeats.map((seat) => seat.id).join(", ")}
                      </span>
                      <span className="total-price">
                        Tổng: {totalPrice.toLocaleString("vi-VN")}đ
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
      </div>
    </header>
  );
};

export default Header;
