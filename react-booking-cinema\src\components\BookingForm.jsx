import { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { updateCustomerInfo, confirmBooking } from '../store/slices/bookingSlice';
import { confirmBooking as confirmSeatBooking } from '../store/slices/seatsSlice';
import { clearSelection } from '../store/slices/moviesSlice';

const BookingForm = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { currentBooking, isBookingInProgress } = useSelector(state => state.booking);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: ''
  });
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Vui lòng nhập họ tên';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'Vui lòng nhập email';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email không hợp lệ';
    }
    
    if (!formData.phone.trim()) {
      newErrors.phone = 'Vui lòng nhập số điện thoại';
    } else if (!/^[0-9]{10,11}$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Số điện thoại không hợp lệ';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      // Update customer info in Redux
      dispatch(updateCustomerInfo(formData));
      
      // Confirm booking
      dispatch(confirmBooking());
      dispatch(confirmSeatBooking());
      dispatch(clearSelection());
      
      // Navigate to success page
      navigate('/booking-success');
    }
  };

  if (!isBookingInProgress || !currentBooking.movie) {
    navigate('/');
    return null;
  }

  return (
    <div className="booking-form">
      <div className="container">
        <div className="booking-content">
          <div className="booking-summary-card">
            <h3>Thông tin đặt vé</h3>
            
            <div className="booking-details">
              <div className="detail-item">
                <span className="label">Phim:</span>
                <span className="value">{currentBooking.movie.title}</span>
              </div>
              
              <div className="detail-item">
                <span className="label">Suất chiếu:</span>
                <span className="value">{currentBooking.showtime}</span>
              </div>
              
              <div className="detail-item">
                <span className="label">Ghế:</span>
                <span className="value">
                  {currentBooking.seats.map(seat => seat.id).join(', ')}
                </span>
              </div>
              
              <div className="detail-item">
                <span className="label">Mã đặt vé:</span>
                <span className="value">{currentBooking.bookingId}</span>
              </div>
              
              <div className="detail-item total">
                <span className="label">Tổng tiền:</span>
                <span className="value price">
                  {currentBooking.totalPrice.toLocaleString('vi-VN')}đ
                </span>
              </div>
            </div>
          </div>

          <div className="customer-form-card">
            <h3>Thông tin khách hàng</h3>
            
            <form onSubmit={handleSubmit} className="customer-form">
              <div className="form-group">
                <label htmlFor="name">Họ và tên *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={errors.name ? 'error' : ''}
                  placeholder="Nhập họ và tên"
                />
                {errors.name && <span className="error-message">{errors.name}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="email">Email *</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={errors.email ? 'error' : ''}
                  placeholder="Nhập email"
                />
                {errors.email && <span className="error-message">{errors.email}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="phone">Số điện thoại *</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className={errors.phone ? 'error' : ''}
                  placeholder="Nhập số điện thoại"
                />
                {errors.phone && <span className="error-message">{errors.phone}</span>}
              </div>

              <div className="form-actions">
                <button 
                  type="button" 
                  className="btn-secondary"
                  onClick={() => navigate('/seat-selection')}
                >
                  Quay lại
                </button>
                <button type="submit" className="btn-primary">
                  Xác nhận đặt vé
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingForm;
