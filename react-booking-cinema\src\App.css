/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: #ffffff;
  min-height: 100vh;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.header {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.logo {
  text-decoration: none;
  color: #ff6b35;
}

.logo h1 {
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0;
}

.nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #ffffff;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  background: #ff6b35;
  color: #ffffff;
}

/* Booking Progress */
.booking-progress {
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem 0;
  margin-top: 1rem;
  border-radius: 10px;
}

.progress-steps {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1rem;
}

.step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.step.active,
.step.completed {
  opacity: 1;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.step.active .step-number {
  background: #ff6b35;
}

.step.completed .step-number {
  background: #4caf50;
}

.booking-info {
  text-align: center;
  font-size: 0.9rem;
}

.movie-info {
  margin-bottom: 0.5rem;
}

.seat-info {
  display: flex;
  justify-content: center;
  gap: 1rem;
  align-items: center;
}

.total-price {
  color: #ff6b35;
  font-weight: bold;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 2rem 0;
}

/* Movie List Styles */
.movie-list h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #ff6b35;
  font-size: 2.5rem;
}

.movies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.movie-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.movie-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
}

.movie-card.selected {
  border-color: #ff6b35;
  box-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
}

.movie-poster {
  position: relative;
  height: 200px;
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
}

.movie-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.movie-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
}

.movie-rating {
  background: rgba(0, 0, 0, 0.8);
  padding: 0.3rem 0.6rem;
  border-radius: 15px;
  font-size: 0.9rem;
}

.movie-info {
  padding: 1.5rem;
}

.movie-title {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: #ffffff;
}

.movie-duration,
.movie-genre {
  color: #cccccc;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
}

.movie-prices {
  margin-top: 1rem;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.seat-type {
  padding: 0.2rem 0.5rem;
  border-radius: 5px;
  font-size: 0.8rem;
  font-weight: bold;
}

.seat-type.standard {
  background: #4caf50;
}

.seat-type.vip {
  background: #ff9800;
}

.seat-type.couple {
  background: #e91e63;
}

.price {
  font-weight: bold;
  color: #ff6b35;
}

/* Showtime Selection */
.showtime-selection {
  background: rgba(255, 255, 255, 0.05);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
}

.showtime-selection h3 {
  margin-bottom: 1.5rem;
  color: #ff6b35;
}

.showtimes {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.showtime-btn {
  padding: 0.8rem 1.5rem;
  border: 2px solid #ff6b35;
  background: transparent;
  color: #ff6b35;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
}

.showtime-btn:hover,
.showtime-btn.selected {
  background: #ff6b35;
  color: #ffffff;
}

.continue-btn {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: #ffffff;
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.continue-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
}

/* Seat Selection Styles */
.seat-selection {
  min-height: 100vh;
  background: url("https://movie-booking-project.vercel.app/img/bgmovie.jpg")
    center/cover no-repeat;
  position: relative;
}

.seat-selection::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1;
}

.seat-selection-container {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 350px;
  min-height: 100vh;
}

.cinema-section {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.cinema-header {
  margin-bottom: 2rem;
}

.cinema-header h2 {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: #ffffff;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
  margin: 0;
}

.screen-section {
  margin-bottom: 2rem;
}

.screen {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: #ffffff;
  padding: 0.8rem 4rem;
  border-radius: 25px;
  font-weight: bold;
  font-size: 1rem;
  text-align: center;
}

.seat-map {
  background: rgba(0, 0, 0, 0.6);
  padding: 2rem;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.seat-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  gap: 1rem;
}

.row-label {
  width: 30px;
  text-align: center;
  font-weight: bold;
  color: #ff6b35;
  font-size: 1.1rem;
}

.seats {
  display: flex;
  gap: 0.3rem;
  flex: 1;
  justify-content: center;
}

.seat {
  width: 30px;
  height: 30px;
  border: 1px solid #666;
  background: #4caf50;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.7rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.seat:hover:not(.occupied) {
  transform: scale(1.1);
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

.seat.standard {
  background: #4caf50;
  border-color: #4caf50;
}

.seat.vip {
  background: #ff9800;
  border-color: #ff9800;
}

.seat.couple {
  background: #e91e63;
  border-color: #e91e63;
  width: 65px;
}

.seat.occupied {
  background: #666;
  border-color: #666;
  cursor: not-allowed;
  opacity: 0.7;
}

.seat.selected {
  background: #ff6b35 !important;
  border-color: #ff6b35 !important;
  box-shadow: 0 0 8px rgba(255, 107, 53, 0.8);
  transform: scale(1.1);
}

/* Booking Sidebar */
.booking-sidebar {
  background: rgba(0, 0, 0, 0.9);
  padding: 2rem;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.booking-info-card {
  background: rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.booking-info-card h3 {
  color: #ff6b35;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  text-align: center;
  font-weight: bold;
}

.legend {
  margin-bottom: 2rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 0.8rem;
  font-size: 0.9rem;
}

.seat-demo {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  border: 1px solid;
}

.seat-demo.standard {
  background: #666;
  border-color: #666;
}

.seat-demo.vip {
  background: #4caf50;
  border-color: #4caf50;
}

.seat-demo.couple {
  background: #ff9800;
  border-color: #ff9800;
}

.seat-demo.selected {
  background: #ff6b35;
  border-color: #ff6b35;
}

.movie-info-sidebar {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.movie-info-sidebar h4 {
  color: #ffffff;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.movie-info-sidebar p {
  color: #cccccc;
  font-size: 0.9rem;
}

.selected-seats-info h4 {
  color: #ffffff;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.selected-seats-list {
  margin-bottom: 1.5rem;
}

.seat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.seat-item:last-child {
  border-bottom: none;
}

.seat-name {
  color: #ffffff;
  font-weight: bold;
}

.seat-price {
  color: #ff6b35;
  font-weight: bold;
}

.total-section {
  margin-bottom: 2rem;
  padding-top: 1rem;
  border-top: 2px solid #ff6b35;
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.2rem;
  font-weight: bold;
}

.total-price .price {
  color: #ff6b35;
}

.continue-btn {
  width: 100%;
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: #ffffff;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.continue-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
}

.no-seats-selected {
  text-align: center;
  color: #cccccc;
  font-style: italic;
  padding: 2rem 0;
}

/* Booking Form Styles */
.booking-form {
  max-width: 1000px;
  margin: 0 auto;
}

.booking-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.booking-summary-card,
.customer-form-card {
  background: rgba(255, 255, 255, 0.05);
  padding: 2rem;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.booking-summary-card h3,
.customer-form-card h3 {
  color: #ff6b35;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.booking-details {
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item.total {
  font-size: 1.2rem;
  font-weight: bold;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 2px solid #ff6b35;
}

.label {
  color: #cccccc;
}

.value {
  color: #ffffff;
  font-weight: 500;
}

.value.price {
  color: #ff6b35;
  font-weight: bold;
}

.customer-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  color: #ffffff;
  font-weight: 500;
}

.form-group input {
  padding: 0.8rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #ff6b35;
}

.form-group input.error {
  border-color: #f44336;
}

.error-message {
  color: #f44336;
  font-size: 0.9rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.btn-primary,
.btn-secondary {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.btn-primary {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: #ffffff;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
}

.btn-secondary {
  background: transparent;
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Booking Success Styles */
.booking-success {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.success-content {
  background: rgba(255, 255, 255, 0.05);
  padding: 3rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.success-icon {
  margin-bottom: 2rem;
}

.checkmark {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #4caf50;
  color: #ffffff;
  font-size: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  animation: checkmarkPulse 0.6s ease-in-out;
}

@keyframes checkmarkPulse {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.success-content h1 {
  color: #4caf50;
  margin-bottom: 1rem;
  font-size: 2.5rem;
}

.success-message {
  color: #cccccc;
  margin-bottom: 2rem;
  font-size: 1.1rem;
  line-height: 1.6;
}

.ticket-info {
  background: rgba(0, 0, 0, 0.3);
  padding: 2rem;
  border-radius: 15px;
  margin: 2rem 0;
  text-align: left;
}

.ticket-info h3 {
  color: #ff6b35;
  margin-bottom: 1.5rem;
  text-align: center;
}

.ticket-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row.total {
  font-size: 1.2rem;
  font-weight: bold;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 2px solid #ff6b35;
}

.booking-id {
  font-family: "Courier New", monospace;
  background: rgba(255, 107, 53, 0.2);
  padding: 0.2rem 0.5rem;
  border-radius: 5px;
}

.important-notes {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  padding: 1.5rem;
  border-radius: 10px;
  margin: 2rem 0;
  text-align: left;
}

.important-notes h4 {
  color: #ffc107;
  margin-bottom: 1rem;
}

.important-notes ul {
  list-style: none;
  padding: 0;
}

.important-notes li {
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  position: relative;
}

.important-notes li:before {
  content: "⚠️";
  position: absolute;
  left: 0;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.action-buttons .btn-primary,
.action-buttons .btn-secondary {
  flex: none;
  padding: 1rem 2rem;
}

/* Booking History Styles */
.booking-history h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #ff6b35;
  font-size: 2.5rem;
}

.empty-history {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-history h3 {
  color: #ffffff;
  margin-bottom: 1rem;
}

.empty-history p {
  color: #cccccc;
  margin-bottom: 2rem;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.booking-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.booking-card:hover {
  transform: translateY(-2px);
}

.booking-header {
  background: rgba(0, 0, 0, 0.3);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.booking-id {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
}

.status-confirmed {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid #4caf50;
}

.status-cancelled {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid #f44336;
}

.booking-date {
  color: #cccccc;
  font-size: 0.9rem;
}

.booking-body {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 2rem;
}

.movie-info h4 {
  color: #ff6b35;
  margin-bottom: 0.5rem;
}

.movie-info p,
.customer-info p {
  color: #cccccc;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
}

.seat-info h5,
.customer-info h5 {
  color: #ffffff;
  margin-bottom: 0.8rem;
  font-size: 1rem;
}

.seats-list {
  display: flex;
  gap: 0.3rem;
  flex-wrap: wrap;
}

.seat-tag {
  padding: 0.2rem 0.5rem;
  border-radius: 5px;
  font-size: 0.8rem;
  font-weight: bold;
  color: #ffffff;
}

.seat-tag.standard {
  background: #4caf50;
}

.seat-tag.vip {
  background: #ff9800;
}

.seat-tag.couple {
  background: #e91e63;
}

.booking-footer {
  background: rgba(0, 0, 0, 0.2);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel {
  background: transparent;
  color: #f44336;
  border: 2px solid #f44336;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel:hover {
  background: #f44336;
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .seat-selection-container {
    grid-template-columns: 1fr 300px;
  }

  .booking-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .nav {
    gap: 1rem;
  }

  .movies-grid {
    grid-template-columns: 1fr;
  }

  .showtimes {
    flex-direction: column;
    align-items: center;
  }

  /* Seat Selection Mobile */
  .seat-selection-container {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }

  .cinema-section {
    padding: 1rem;
  }

  .cinema-header h2 {
    font-size: 1rem;
    padding: 0.8rem 1.5rem;
  }

  .seat-map {
    padding: 1rem;
  }

  .seats {
    gap: 0.2rem;
  }

  .seat {
    width: 25px;
    height: 25px;
    font-size: 0.6rem;
  }

  .seat.couple {
    width: 55px;
  }

  .booking-sidebar {
    padding: 1rem;
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .booking-info-card {
    padding: 1rem;
  }

  .booking-info-card h3 {
    font-size: 1rem;
  }

  .booking-body {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }

  .progress-steps {
    gap: 1rem;
  }

  .step {
    flex-direction: column;
    text-align: center;
  }
}
