import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate, Link } from 'react-router-dom';

const BookingSuccess = () => {
  const navigate = useNavigate();
  const { bookingHistory } = useSelector(state => state.booking);
  
  // Get the latest booking (most recent one)
  const latestBooking = bookingHistory[bookingHistory.length - 1];

  useEffect(() => {
    // If no booking found, redirect to home
    if (!latestBooking) {
      navigate('/');
    }
  }, [latestBooking, navigate]);

  if (!latestBooking) {
    return null;
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="booking-success">
      <div className="container">
        <div className="success-content">
          <div className="success-icon">
            <div className="checkmark">✓</div>
          </div>
          
          <h1>Đặt vé thành công!</h1>
          <p className="success-message">
            Cảm ơn bạn đã đặt vé tại CyberSoft Cinema. 
            Thông tin vé đã được gửi đến email của bạn.
          </p>

          <div className="ticket-info">
            <h3>Thông tin vé của bạn</h3>
            
            <div className="ticket-details">
              <div className="detail-row">
                <span className="label">Mã đặt vé:</span>
                <span className="value booking-id">{latestBooking.bookingId}</span>
              </div>
              
              <div className="detail-row">
                <span className="label">Phim:</span>
                <span className="value">{latestBooking.movie.title}</span>
              </div>
              
              <div className="detail-row">
                <span className="label">Suất chiếu:</span>
                <span className="value">{latestBooking.showtime}</span>
              </div>
              
              <div className="detail-row">
                <span className="label">Ghế:</span>
                <span className="value">
                  {latestBooking.seats.map(seat => seat.id).join(', ')}
                </span>
              </div>
              
              <div className="detail-row">
                <span className="label">Khách hàng:</span>
                <span className="value">{latestBooking.customerInfo.name}</span>
              </div>
              
              <div className="detail-row">
                <span className="label">Ngày đặt:</span>
                <span className="value">{formatDate(latestBooking.bookingDate)}</span>
              </div>
              
              <div className="detail-row total">
                <span className="label">Tổng tiền:</span>
                <span className="value price">
                  {latestBooking.totalPrice.toLocaleString('vi-VN')}đ
                </span>
              </div>
            </div>
          </div>

          <div className="important-notes">
            <h4>Lưu ý quan trọng:</h4>
            <ul>
              <li>Vui lòng đến rạp trước giờ chiếu 15 phút để làm thủ tục</li>
              <li>Mang theo mã đặt vé và giấy tờ tùy thân</li>
              <li>Vé đã đặt không thể hoàn trả sau 2 giờ trước giờ chiếu</li>
              <li>Liên hệ hotline: 1900-xxxx nếu cần hỗ trợ</li>
            </ul>
          </div>

          <div className="action-buttons">
            <Link to="/" className="btn-primary">
              Đặt vé phim khác
            </Link>
            <Link to="/booking-history" className="btn-secondary">
              Xem lịch sử đặt vé
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingSuccess;
