import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  movies: [
    {
      id: 1,
      title: "Avengers: Endgame",
      poster: "/bgmovie.jpg",
      duration: "181 phút",
      genre: "<PERSON><PERSON><PERSON> động, <PERSON><PERSON><PERSON> lưu",
      rating: "8.4",
      showtimes: ["14:00", "17:00", "20:00", "22:30"],
      price: {
        standard: 75000,
        vip: 120000,
        couple: 150000
      }
    },
    {
      id: 2,
      title: "Spider-Man: No Way Home",
      poster: "/bgmovie.jpg",
      duration: "148 phút",
      genre: "Hành động, <PERSON><PERSON><PERSON> lưu",
      rating: "8.7",
      showtimes: ["13:30", "16:30", "19:30", "22:00"],
      price: {
        standard: 75000,
        vip: 120000,
        couple: 150000
      }
    },
    {
      id: 3,
      title: "The Batman",
      poster: "/bgmovie.jpg",
      duration: "176 phút",
      genre: "Hành động, <PERSON><PERSON><PERSON> phạm",
      rating: "7.8",
      showtimes: ["15:00", "18:00", "21:00"],
      price: {
        standard: 75000,
        vip: 120000,
        couple: 150000
      }
    }
  ],
  selectedMovie: null,
  selectedShowtime: null
};

const moviesSlice = createSlice({
  name: 'movies',
  initialState,
  reducers: {
    selectMovie: (state, action) => {
      state.selectedMovie = action.payload;
    },
    selectShowtime: (state, action) => {
      state.selectedShowtime = action.payload;
    },
    clearSelection: (state) => {
      state.selectedMovie = null;
      state.selectedShowtime = null;
    }
  }
});

export const { selectMovie, selectShowtime, clearSelection } = moviesSlice.actions;
export default moviesSlice.reducer;
