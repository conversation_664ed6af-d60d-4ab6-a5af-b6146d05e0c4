import React from 'react';
import { Mo<PERSON>, Button } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';

const SuccessModal = ({ show, onHide, type, bookingData }) => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    onHide();
    navigate('/');
  };

  const handleViewHistory = () => {
    onHide();
    navigate('/booking-history');
  };

  const getModalContent = () => {
    switch (type) {
      case 'booking-success':
        return {
          title: '🎉 Đặt vé thành công!',
          icon: '✅',
          message: 'Vé của bạn đã được đặt thành công. Vui lòng thanh toán để hoàn tất.',
          buttonText: 'Tiếp tục thanh toán',
          buttonVariant: 'success'
        };
      case 'payment-success':
        return {
          title: '💳 Thanh toán thành công!',
          icon: '🎊',
          message: 'Cảm ơn bạn đã sử dụng dịch vụ. <PERSON><PERSON><PERSON> bạn xem phim vui vẻ!',
          buttonText: 'Về trang chủ',
          buttonVariant: 'primary'
        };
      case 'booking-confirmed':
        return {
          title: '🎬 Xác nhận đặt vé',
          icon: '📋',
          message: 'Thông tin đặt vé của bạn đã được xác nhận.',
          buttonText: 'Xem lịch sử',
          buttonVariant: 'info'
        };
      default:
        return {
          title: 'Thông báo',
          icon: 'ℹ️',
          message: 'Thao tác đã được thực hiện thành công.',
          buttonText: 'Đóng',
          buttonVariant: 'secondary'
        };
    }
  };

  const content = getModalContent();

  return (
    <Modal 
      show={show} 
      onHide={onHide} 
      centered 
      backdrop="static"
      keyboard={false}
      className="success-modal"
    >
      <Modal.Header className="border-0 text-center">
        <Modal.Title className="w-100 text-center">
          <div className="modal-icon mb-3">
            <span style={{ fontSize: '4rem' }}>{content.icon}</span>
          </div>
          <h3 className="text-gradient">{content.title}</h3>
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body className="text-center px-4">
        <p className="lead mb-4">{content.message}</p>
        
        {bookingData && (
          <div className="booking-details">
            <div className="card bg-light">
              <div className="card-body">
                <h5 className="card-title text-primary">{bookingData.movieTitle}</h5>
                <div className="row text-start">
                  <div className="col-6">
                    <small className="text-muted">Suất chiếu:</small>
                    <div className="fw-bold">{bookingData.showtime}</div>
                  </div>
                  <div className="col-6">
                    <small className="text-muted">Ghế:</small>
                    <div className="fw-bold">{bookingData.seats?.join(', ')}</div>
                  </div>
                  <div className="col-6 mt-2">
                    <small className="text-muted">Tổng tiền:</small>
                    <div className="fw-bold text-success">
                      {bookingData.totalPrice?.toLocaleString('vi-VN')}đ
                    </div>
                  </div>
                  <div className="col-6 mt-2">
                    <small className="text-muted">Mã đặt vé:</small>
                    <div className="fw-bold text-info">{bookingData.bookingId}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal.Body>
      
      <Modal.Footer className="border-0 justify-content-center">
        <div className="d-grid gap-2 d-md-flex justify-content-md-center w-100">
          {type === 'payment-success' ? (
            <>
              <Button 
                variant="outline-secondary" 
                onClick={handleViewHistory}
                className="me-md-2"
              >
                Xem lịch sử
              </Button>
              <Button 
                variant={content.buttonVariant} 
                onClick={handleGoHome}
                size="lg"
              >
                {content.buttonText}
              </Button>
            </>
          ) : (
            <Button 
              variant={content.buttonVariant} 
              onClick={onHide}
              size="lg"
              className="px-5"
            >
              {content.buttonText}
            </Button>
          )}
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default SuccessModal;
