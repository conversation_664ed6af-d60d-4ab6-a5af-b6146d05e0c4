import { createSlice } from '@reduxjs/toolkit';

// Tạo layout ghế cho rạp chiếu phim
const createSeatLayout = () => {
  const rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'];
  const seats = [];
  
  rows.forEach((row, rowIndex) => {
    for (let seatNumber = 1; seatNumber <= 16; seatNumber++) {
      let seatType = 'standard';
      let isOccupied = false;
      
      // VIP seats (rows A, B)
      if (rowIndex < 2) {
        seatType = 'vip';
      }
      // Couple seats (rows I, J - seats 7-10)
      else if (rowIndex >= 8 && seatNumber >= 7 && seatNumber <= 10) {
        seatType = 'couple';
      }
      
      // Random occupied seats for demo
      if (Math.random() < 0.15) {
        isOccupied = true;
      }
      
      seats.push({
        id: `${row}${seatNumber}`,
        row: row,
        number: seatNumber,
        type: seatType,
        isOccupied: isOccupied,
        isSelected: false
      });
    }
  });
  
  return seats;
};

const initialState = {
  seats: createSeatLayout(),
  selectedSeats: [],
  totalPrice: 0
};

const seatsSlice = createSlice({
  name: 'seats',
  initialState,
  reducers: {
    toggleSeat: (state, action) => {
      const seatId = action.payload;
      const seat = state.seats.find(s => s.id === seatId);
      
      if (seat && !seat.isOccupied) {
        seat.isSelected = !seat.isSelected;
        
        if (seat.isSelected) {
          state.selectedSeats.push(seat);
        } else {
          state.selectedSeats = state.selectedSeats.filter(s => s.id !== seatId);
        }
      }
    },
    calculateTotalPrice: (state, action) => {
      const moviePrices = action.payload;
      let total = 0;
      
      state.selectedSeats.forEach(seat => {
        total += moviePrices[seat.type] || 0;
      });
      
      state.totalPrice = total;
    },
    clearSelectedSeats: (state) => {
      state.seats.forEach(seat => {
        seat.isSelected = false;
      });
      state.selectedSeats = [];
      state.totalPrice = 0;
    },
    confirmBooking: (state) => {
      state.selectedSeats.forEach(selectedSeat => {
        const seat = state.seats.find(s => s.id === selectedSeat.id);
        if (seat) {
          seat.isOccupied = true;
          seat.isSelected = false;
        }
      });
      state.selectedSeats = [];
      state.totalPrice = 0;
    }
  }
});

export const { 
  toggleSeat, 
  calculateTotalPrice, 
  clearSelectedSeats, 
  confirmBooking 
} = seatsSlice.actions;

export default seatsSlice.reducer;
