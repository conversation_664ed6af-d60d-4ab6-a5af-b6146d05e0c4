{"hash": "00d9519f", "configHash": "a4a3bd1a", "lockfileHash": "87e86135", "browserHash": "dfd1a665", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "9e702f35", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "2a0dcd6d", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "455043fc", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "420e67b4", "needsInterop": true}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "4276534d", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "b6876044", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "3eb546d5", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "f8673513", "needsInterop": false}}, "chunks": {"chunk-E7Q22A2S": {"file": "chunk-E7Q22A2S.js"}, "chunk-6P6Q65E3": {"file": "chunk-6P6Q65E3.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}