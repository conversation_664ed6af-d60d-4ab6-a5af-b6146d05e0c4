import React from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';

const ErrorModal = ({ show, onHide, title, message, type = 'error' }) => {
  const getModalConfig = () => {
    switch (type) {
      case 'warning':
        return {
          icon: '⚠️',
          iconColor: '#ffc107',
          headerClass: 'bg-warning text-dark',
          buttonVariant: 'warning'
        };
      case 'info':
        return {
          icon: 'ℹ️',
          iconColor: '#17a2b8',
          headerClass: 'bg-info text-white',
          buttonVariant: 'info'
        };
      case 'error':
      default:
        return {
          icon: '❌',
          iconColor: '#dc3545',
          headerClass: 'bg-danger text-white',
          buttonVariant: 'danger'
        };
    }
  };

  const config = getModalConfig();

  return (
    <Modal 
      show={show} 
      onHide={onHide} 
      centered 
      backdrop="static"
      className="error-modal"
    >
      <Modal.Header className={`border-0 ${config.headerClass}`}>
        <Modal.Title className="w-100 text-center">
          <div className="modal-icon mb-2">
            <span style={{ fontSize: '3rem' }}>{config.icon}</span>
          </div>
          <h4>{title || 'Có lỗi xảy ra'}</h4>
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body className="text-center px-4 py-4">
        <p className="lead mb-0">
          {message || 'Đã có lỗi xảy ra. Vui lòng thử lại sau.'}
        </p>
      </Modal.Body>
      
      <Modal.Footer className="border-0 justify-content-center">
        <Button 
          variant={config.buttonVariant} 
          onClick={onHide}
          size="lg"
          className="px-4"
        >
          Đóng
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ErrorModal;
