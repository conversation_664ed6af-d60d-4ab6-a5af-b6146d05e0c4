import { Routes, Route } from "react-router-dom";
import Header from "./components/Header";
import MovieList from "./components/MovieList";
import SeatSelection from "./components/SeatSelection";
import BookingForm from "./components/BookingForm";
import BookingHistory from "./components/BookingHistory";
import BookingSuccess from "./components/BookingSuccess";
import "./App.css";

function App() {
  return (
    <div className="App">
      <Header />
      <main className="main-content">
        <Routes>
          <Route path="/" element={<MovieList />} />
          <Route path="/seat-selection" element={<SeatSelection />} />
          <Route path="/booking" element={<BookingForm />} />
          <Route path="/booking-success" element={<BookingSuccess />} />
          <Route path="/booking-history" element={<BookingHistory />} />
        </Routes>
      </main>
    </div>
  );
}

export default App;
